// Simple session management for admin authentication
const ADMIN_CREDENTIALS = {
  username: "admin",
  password: "password123"
};

const SESSION_COOKIE_NAME = "admin_session";

export function validateCredentials(username: string, password: string): boolean {
  return username === ADMIN_CREDENTIALS.username && password === ADMIN_CREDENTIALS.password;
}

export function createSession(): string {
  // Simple session token (in production, use proper JWT or secure session management)
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

export function getSessionCookie(request: Request): string | null {
  const cookieHeader = request.headers.get("Cookie");
  if (!cookieHeader) return null;
  
  const cookies = cookieHeader.split(";").reduce((acc, cookie) => {
    const [name, value] = cookie.trim().split("=");
    acc[name] = value;
    return acc;
  }, {} as Record<string, string>);
  
  return cookies[SESSION_COOKIE_NAME] || null;
}

export function createSessionCookie(sessionToken: string): string {
  return `${SESSION_COOKIE_NAME}=${sessionToken}; HttpOnly; Path=/; Max-Age=86400`; // 24 hours
}

export function clearSessionCookie(): string {
  return `${SESSION_COOKIE_NAME}=; HttpOnly; Path=/; Max-Age=0`;
}

// Simple in-memory session store (in production, use Redis or database)
const activeSessions = new Set<string>();

export function isValidSession(sessionToken: string | null): boolean {
  return sessionToken ? activeSessions.has(sessionToken) : false;
}

export function addSession(sessionToken: string): void {
  activeSessions.add(sessionToken);
}

export function removeSession(sessionToken: string): void {
  activeSessions.delete(sessionToken);
}
