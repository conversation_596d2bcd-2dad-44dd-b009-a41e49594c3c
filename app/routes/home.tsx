import type { Route } from "./+types/home";
import { useLoaderData, Link } from "react-router";
import Database from "better-sqlite3";
import { Welcome } from "../welcome/welcome";

// --- SERVER CODE ---
const db = new Database("data.sqlite");
db.exec(`CREATE TABLE IF NOT EXISTS templates (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  template_id TEXT NOT NULL UNIQUE
)`);

export async function loader() {
  // Remove all rows except id=1
  db.prepare("DELETE FROM templates WHERE id != 1").run();

  // Ensure id=1 exists
  let row = db.prepare("SELECT template_id FROM templates WHERE id = 1").get();
  if (!row) {
    db.prepare("INSERT INTO templates (id, template_id) VALUES (?, ?)").run(
      1,
      "default-template"
    );
    row = db.prepare("SELECT template_id FROM templates WHERE id = 1").get();
  }
  return row;
}

export function meta({}: Route.MetaArgs) {
  return [
    { title: "New React Router App" },
    { name: "description", content: "Welcome to React Router!" },
  ];
}

export default function Home() {
  const data = useLoaderData<typeof loader>();

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header with Template ID */}
      <div className="bg-white dark:bg-gray-800 shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                My App
              </h1>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  Template ID:
                </span>
                <span className="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-md font-mono text-sm">
                  {data?.template_id || "Not set"}
                </span>
              </div>
            </div>
            <Link
              to="/login"
              className="text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white text-sm font-medium"
            >
              Admin Login
            </Link>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="py-8">
        <Welcome />
      </div>
    </div>
  );
}
