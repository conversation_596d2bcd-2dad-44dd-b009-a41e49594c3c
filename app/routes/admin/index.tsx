import type { Route } from "./+types/index";
import { use<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, redirect } from "react-router";
import { getSessionCookie, isValidSession } from "../../utils/auth";

// --- SERVER CODE ---
function getDatabase() {
  if (typeof window !== "undefined") {
    throw new Error("Database should only be accessed on the server");
  }

  const Database = require("better-sqlite3");
  const db = new Database("data.sqlite");
  db.exec(`CREATE TABLE IF NOT EXISTS templates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    template_id TEXT NOT NULL UNIQUE
  )`);
  return db;
}

export async function loader({ request }: Route.LoaderArgs) {
  // Check authentication
  const sessionToken = getSessionCookie(request);
  if (!isValidSession(sessionToken)) {
    throw redirect("/login");
  }

  const db = getDatabase();

  // Remove all rows except id=1
  db.prepare("DELETE FROM templates WHERE id != 1").run();

  // Ensure id=1 exists
  let row = db.prepare("SELECT template_id FROM templates WHERE id = 1").get();
  if (!row) {
    db.prepare("INSERT INTO templates (id, template_id) VALUES (?, ?)").run(
      1,
      "default-template"
    );
    row = db.prepare("SELECT template_id FROM templates WHERE id = 1").get();
  }
  return row;
}

// --- CLIENT CODE ---
export function meta({}: Route.MetaArgs) {
  return [
    { title: "Admin Dashboard" },
    { name: "description", content: "Admin dashboard" },
  ];
}

export default function AdminDashboard() {
  const data = useLoaderData<typeof loader>();

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          Admin Dashboard
        </h1>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          Welcome to the admin panel. Manage your application settings here.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Current Template Status */}
        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg
                  className="h-6 w-6 text-gray-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Current Template ID
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {data?.template_id || "Not set"}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 dark:bg-gray-700 px-5 py-3">
            <div className="text-sm">
              <Link
                to="/admin/template"
                className="font-medium text-indigo-600 dark:text-indigo-400 hover:text-indigo-500"
              >
                Manage template →
              </Link>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg
                  className="h-6 w-6 text-gray-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Quick Actions
                  </dt>
                  <dd className="text-sm text-gray-900 dark:text-white">
                    Manage your application
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 dark:bg-gray-700 px-5 py-3">
            <div className="text-sm space-y-2">
              <Link
                to="/admin/template"
                className="block font-medium text-indigo-600 dark:text-indigo-400 hover:text-indigo-500"
              >
                Update Template ID →
              </Link>
              <Link
                to="/"
                className="block font-medium text-indigo-600 dark:text-indigo-400 hover:text-indigo-500"
              >
                View Homepage →
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
