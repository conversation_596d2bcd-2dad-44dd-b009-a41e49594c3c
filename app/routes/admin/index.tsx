import type { Route } from "./+types/index";
import React from "react";
import { useLoaderData, useFetcher } from "react-router";
import Database from "better-sqlite3";

// --- SERVER CODE ---
const db = new Database("data.sqlite");
db.exec(`CREATE TABLE IF NOT EXISTS templates (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  template_id TEXT NOT NULL UNIQUE
)`);

export async function loader() {
  // Remove all rows except id=1
  db.prepare("DELETE FROM templates WHERE id != 1").run();

  // Ensure id=1 exists
  let row = db.prepare("SELECT template_id FROM templates WHERE id = 1").get();
  if (!row) {
    db.prepare("INSERT INTO templates (id, template_id) VALUES (?, ?)").run(
      1,
      "1"
    );
    row = db.prepare("SELECT template_id FROM templates WHERE id = 1").get();
  }
  return row;
}

export async function action({ request }: Route.ActionArgs) {
  const formData = await request.formData();
  const templateId = formData.get("template_id");

  if (request.method === "POST" && templateId) {
    db.prepare("UPDATE templates SET template_id = ? WHERE id = 1").run(
      templateId
    );
    return { success: true };
  }

  return { error: "Invalid request" };
}

// --- CLIENT CODE ---
interface Template {
  template_id: string;
}

export default function Admin() {
  const data = useLoaderData() as { template_id: string };
  const fetcher = useFetcher();
  const [newId, setNewId] = React.useState("");

  return (
    <div className="max-w-xl mx-auto py-10">
      <h1 className="text-2xl font-bold mb-6">Admin: Manage Template ID</h1>
      <fetcher.Form method="post" className="flex gap-2 mb-4">
        <input
          className="border px-2 py-1 flex-1 rounded"
          name="template_id"
          value={newId}
          onChange={(e) => setNewId(e.target.value)}
          placeholder="New Template ID"
        />
        <button
          type="submit"
          className="bg-blue-600 text-white px-4 py-1 rounded"
        >
          Update
        </button>
      </fetcher.Form>
      {fetcher.data?.error && (
        <div className="text-red-600 mb-2">{fetcher.data.error}</div>
      )}
      <div className="border rounded p-4 bg-white dark:bg-gray-800">
        <div className="flex justify-between items-center py-1">
          <span>Current Template ID:</span>
          <span className="font-mono">{data.template_id}</span>
        </div>
      </div>
    </div>
  );
}
