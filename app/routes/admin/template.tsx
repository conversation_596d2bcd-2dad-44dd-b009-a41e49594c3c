import type { Route } from "./+types/template";
import React from "react";
import { use<PERSON><PERSON><PERSON><PERSON><PERSON>, useFetcher, redirect } from "react-router";
import { getSessionCookie, isValidSession } from "../../utils/auth";

// --- SERVER CODE ---
function getDatabase() {
  if (typeof window !== "undefined") {
    throw new Error("Database should only be accessed on the server");
  }

  const Database = require("better-sqlite3");
  const db = new Database("data.sqlite");
  db.exec(`CREATE TABLE IF NOT EXISTS templates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    template_id TEXT NOT NULL UNIQUE
  )`);
  return db;
}

export async function loader({ request }: Route.LoaderArgs) {
  // Check authentication
  const sessionToken = getSessionCookie(request);
  if (!isValidSession(sessionToken)) {
    throw redirect("/login");
  }

  const db = getDatabase();

  // Remove all rows except id=1
  db.prepare("DELETE FROM templates WHERE id != 1").run();

  // Ensure id=1 exists
  let row = db.prepare("SELECT template_id FROM templates WHERE id = 1").get();
  if (!row) {
    db.prepare("INSERT INTO templates (id, template_id) VALUES (?, ?)").run(
      1,
      "default-template"
    );
    row = db.prepare("SELECT template_id FROM templates WHERE id = 1").get();
  }
  return row;
}

export async function action({ request }: Route.ActionArgs) {
  // Check authentication
  const sessionToken = getSessionCookie(request);
  if (!isValidSession(sessionToken)) {
    throw redirect("/login");
  }

  const formData = await request.formData();
  const templateId = formData.get("template_id");

  if (request.method === "POST" && templateId) {
    try {
      const db = getDatabase();
      db.prepare("UPDATE templates SET template_id = ? WHERE id = 1").run(
        templateId
      );
      return { success: true, message: "Template ID updated successfully!" };
    } catch (error) {
      return { error: "Failed to update template ID" };
    }
  }

  return { error: "Invalid request" };
}

// --- CLIENT CODE ---
export function meta({}: Route.MetaArgs) {
  return [
    { title: "Template Management - Admin" },
    { name: "description", content: "Manage template ID" },
  ];
}

export default function Template() {
  const data = useLoaderData<typeof loader>();
  const fetcher = useFetcher();
  const [newId, setNewId] = React.useState("");
  console.log(newId);
  const isSubmitting = fetcher.state === "submitting";

  // Reset input after successful submission
  React.useEffect(() => {
    if (fetcher.data?.success) {
      setNewId("");
    }
  }, [fetcher.data?.success]);

  return (
    <div className="max-w-2xl mx-auto">
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">
            Template ID Management
          </h3>
          <div className="mt-2 max-w-xl text-sm text-gray-500 dark:text-gray-400">
            <p>
              Update the template ID that will be displayed on the homepage.
            </p>
          </div>

          <div className="mt-6">
            <div className="mb-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-md">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Current Template ID:
                </span>
                <span className="font-mono text-lg text-blue-600 dark:text-blue-400">
                  {data?.template_id || "Not set"}
                </span>
              </div>
            </div>

            <fetcher.Form method="post" className="space-y-4">
              <div>
                <label
                  htmlFor="template_id"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                >
                  New Template ID
                </label>
                <div className="mt-1 flex rounded-md shadow-sm">
                  <input
                    type="text"
                    name="template_id"
                    id="template_id"
                    value={newId}
                    onChange={(e) => {
                      console.log("onChange triggered:", e.target.value);
                      setNewId(e.target.value);
                    }}
                    onInput={(e) => {
                      console.log("onInput triggered:", e.currentTarget.value);
                    }}
                    onFocus={() => console.log("Input focused")}
                    onBlur={() => console.log("Input blurred")}
                    className="flex-1 min-w-0 block w-full px-3 py-2 rounded-md border border-gray-300 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    placeholder="Enter new template ID"
                    required
                  />
                  <button
                    type="submit"
                    disabled={isSubmitting || !newId.trim()}
                    className="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSubmitting ? "Updating..." : "Update"}
                  </button>
                </div>
              </div>
            </fetcher.Form>

            {fetcher.data?.success && (
              <div className="mt-4 p-4 bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-md">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg
                      className="h-5 w-5 text-green-400"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-green-800 dark:text-green-200">
                      {fetcher.data.message}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {fetcher.data?.error && (
              <div className="mt-4 p-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg
                      className="h-5 w-5 text-red-400"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-red-800 dark:text-red-200">
                      {fetcher.data.error}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
