import type { Route } from "./+types/layout";
import { Outlet, redirect, Link, Form } from "react-router";
import {
  getSessionCookie,
  isValidSession,
  removeSession,
  clearSessionCookie,
} from "../../utils/auth";

export async function loader({ request }: Route.LoaderArgs) {
  const sessionToken = getSessionCookie(request);
  if (!isValidSession(sessionToken)) {
    throw redirect("/login");
  }
  return {};
}

export async function action({ request }: Route.ActionArgs) {
  if (request.method === "POST") {
    const sessionToken = getSessionCookie(request);
    if (sessionToken) {
      removeSession(sessionToken);
    }

    const headers = new Headers();
    headers.append("Set-Cookie", clearSessionCookie());

    throw redirect("/login", { headers });
  }
  return {};
}

export default function AdminLayout() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <nav className="bg-white dark:bg-gray-800 shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                Admin Panel
              </h1>
              <div className="ml-10 flex items-baseline space-x-4">
                <Link
                  to="/admin"
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium"
                >
                  Dashboard
                </Link>
                <Link
                  to="/admin/template"
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium"
                >
                  Template
                </Link>
              </div>
            </div>
            <div className="flex items-center">
              <Form method="post">
                <button
                  type="submit"
                  className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  Logout
                </button>
              </Form>
            </div>
          </div>
        </div>
      </nav>
      <main className="py-10">
        <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
          <Outlet />
        </div>
      </main>
    </div>
  );
}
