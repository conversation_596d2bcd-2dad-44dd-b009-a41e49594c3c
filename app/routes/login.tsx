import type { Route } from "./+types/login";
import { Form, redirect, useActionData } from "react-router";
import { validateCredentials, createSession, createSessionCookie, addSession, getSessionCookie, isValidSession } from "../utils/auth";

export async function loader({ request }: Route.LoaderArgs) {
  // Check if user is already logged in
  const sessionToken = getSessionCookie(request);
  if (isValidSession(sessionToken)) {
    throw redirect("/admin");
  }
  return {};
}

export async function action({ request }: Route.ActionArgs) {
  const formData = await request.formData();
  const username = formData.get("username") as string;
  const password = formData.get("password") as string;

  if (!username || !password) {
    return { error: "Username and password are required" };
  }

  if (validateCredentials(username, password)) {
    const sessionToken = createSession();
    addSession(sessionToken);
    
    const headers = new Headers();
    headers.append("Set-Cookie", createSessionCookie(sessionToken));
    
    throw redirect("/admin", { headers });
  }

  return { error: "Invalid username or password" };
}

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Admin Login" },
    { name: "description", content: "Admin login page" },
  ];
}

export default function Login() {
  const actionData = useActionData<typeof action>();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white">
            Admin Login
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600 dark:text-gray-400">
            Please sign in to access the admin panel
          </p>
        </div>
        <Form method="post" className="mt-8 space-y-6">
          <div className="rounded-md shadow-sm -space-y-px">
            <div>
              <label htmlFor="username" className="sr-only">
                Username
              </label>
              <input
                id="username"
                name="username"
                type="text"
                required
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
                placeholder="Username"
              />
            </div>
            <div>
              <label htmlFor="password" className="sr-only">
                Password
              </label>
              <input
                id="password"
                name="password"
                type="password"
                required
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
                placeholder="Password"
              />
            </div>
          </div>

          {actionData?.error && (
            <div className="text-red-600 text-sm text-center">
              {actionData.error}
            </div>
          )}

          <div>
            <button
              type="submit"
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Sign in
            </button>
          </div>
        </Form>
        
        <div className="mt-4 p-4 bg-yellow-50 dark:bg-yellow-900 rounded-md">
          <p className="text-sm text-yellow-800 dark:text-yellow-200">
            <strong>Demo Credentials:</strong><br />
            Username: admin<br />
            Password: password123
          </p>
        </div>
      </div>
    </div>
  );
}
